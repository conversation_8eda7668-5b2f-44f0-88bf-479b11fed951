# EPUB阅读器 - 高亮与批注功能

这是一个基于Electron和epub.js的EPUB阅读器，现在支持高亮和批注功能。

## 功能特性

### 基础阅读功能
- 打开和阅读EPUB文件
- 目录导航
- 翻页功能（键盘和按钮）
- 字体大小、行距、主题调整
- 全屏模式
- 阅读进度显示
- 最近阅读历史

### 高亮与批注功能 ✨
- **文本选择**: 在EPUB内容中选择文字时，会自动显示操作菜单
- **黄色高亮**: 支持黄色背景高亮显示
- **批注功能**: 为选中的文字添加文字批注，显示为虚线下划线
- **悬停提示**: 将鼠标悬停在批注文字上即可查看批注内容
- **批注管理**: 查看、编辑、删除所有高亮和批注
- **数据持久化**: 高亮和批注数据自动保存到本地
- **快速定位**: 点击批注列表中的项目可快速跳转到对应位置

## 使用方法

### 启动应用
```bash
npm start
```

### 高亮文字
1. 在EPUB内容中选择要高亮的文字
2. 在弹出的菜单中点击"高亮"按钮
3. 文字会立即显示黄色背景高亮并保存

### 添加批注
1. 在EPUB内容中选择要批注的文字
2. 点击弹出菜单中的"批注"按钮
3. 在弹出的对话框中输入批注内容
4. 点击"保存"完成批注（文字会显示为蓝色虚线下划线）

### 查看批注
1. 将鼠标悬停在有虚线下划线的文字上
2. 会自动显示批注内容的悬浮提示框

### 管理批注
1. 点击工具栏中的"批注"按钮打开批注面板
2. 在批注面板中可以：
   - 查看所有高亮和批注（用不同图标区分）
   - 点击"定位"跳转到对应位置
   - 点击"编辑"修改批注内容（仅批注类型）
   - 点击"删除"移除高亮或批注

### 移除高亮和批注
1. **单个移除**：选择已高亮或批注的文字，在弹出菜单中点击"移除"按钮
2. **批量清除**：点击工具栏中的红色"清除当页"按钮，可一键清除当前页面的所有高亮和批注

## 技术实现

### 核心技术栈
- **Electron**: 桌面应用框架
- **epub.js**: EPUB文件解析和渲染
- **CFI (Canonical Fragment Identifier)**: 用于精确定位EPUB中的文本位置
- **localStorage**: 本地数据存储

### 数据存储
- 高亮和批注数据按书籍分别存储
- 存储键格式：`epub-reader-annotations-{书籍路径}`
- 数据结构包含：ID、类型、CFI范围、颜色、文本、批注、时间戳

### 文本选择检测
- 监听`mouseup`、`touchend`和键盘事件
- 使用epub.js的CFI API获取精确的文本位置
- 动态显示选择菜单，自动调整位置避免超出边界

## 开发说明

### 文件结构
```
├── main.js              # Electron主进程
├── preload.js           # 预加载脚本
├── renderer/
│   ├── index.html       # 渲染进程HTML
│   └── renderer.js      # 渲染进程JavaScript
└── package.json         # 项目配置
```

### 主要功能模块
- `handleTextSelection()`: 处理文本选择
- `addHighlight()`: 添加高亮
- `removeHighlight()`: 移除高亮
- `renderAnnotationsList()`: 渲染批注列表
- `restoreHighlights()`: 恢复已保存的高亮

## 注意事项

1. 高亮和批注数据存储在浏览器的localStorage中
2. 每本书的数据独立存储，不会相互影响
3. 选择菜单会自动调整位置，避免超出视窗边界
4. 支持键盘选择文字（Shift + 方向键）
5. 批注以蓝色虚线下划线显示，悬停查看内容
6. 高亮使用黄色背景显示

## 未来改进

- [ ] 支持导出高亮和批注
- [ ] 支持高亮样式自定义
- [ ] 支持批注的富文本编辑
- [ ] 支持高亮和批注的云同步
- [ ] 支持批注的分类和标签
