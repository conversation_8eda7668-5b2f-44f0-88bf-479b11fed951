<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>EPUB 阅读器</title>
  <style>
    body { font-family: sans-serif; margin: 0; padding: 0; }
    #toolbar { padding: 10px; background: #f0f0f0; }
    #toc {
      width: 220px;
      overflow-y: auto;
      background: #fafafa;
      border-right: 1px solid #eee;
      display: none;
      transition: box-shadow 0.2s;
      box-shadow: 2px 0 8px rgba(0,0,0,0.04);
    }
    #toc h4 {
      margin: 10px 0 10px 10px;
      font-size: 16px;
      color: #333;
    }
    #toc ul {
      list-style: none;
      padding-left: 0;
      margin: 0;
    }
    #toc li {
      cursor: pointer;
      margin: 6px 0;
      padding: 4px 12px 4px 18px;
      border-radius: 4px;
      transition: background 0.2s;
      user-select: none;
      position: relative;
    }
    #toc li:hover {
      background: #e6f7ff;
      color: #1890ff;
    }
    #toc li.toc-collapsed > ul {
      display: none;
    }
    #toc li.toc-expandable:before {
      content: '\25B6';
      position: absolute;
      left: 4px;
      top: 8px;
      font-size: 10px;
      color: #888;
      transition: transform 0.2s;
    }
    #toc li.toc-expanded:before {
      content: '\25BC';
      position: absolute;
      left: 4px;
      top: 8px;
      font-size: 10px;
      color: #888;
      transition: transform 0.2s;
    }
    #main-content {
      display: flex;
      height: 80vh;
      width: 100vw;
    }
    #viewer { flex: 1; min-width: 0; position: relative; }

    /* 选择菜单样式 */
    #selection-menu {
      position: absolute;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      padding: 8px;
      display: none;
      z-index: 1000;
      white-space: nowrap;
    }

    #selection-menu button {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 6px 12px;
      margin: 0 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s;
    }

    #selection-menu button:hover {
      background: #e9ecef;
      border-color: #adb5bd;
    }

    #selection-menu .highlight-btn {
      color: #fff;
      border: none;
    }

    #selection-menu .highlight-btn.yellow { background: #ffc107; }
    #selection-menu .highlight-btn.green { background: #28a745; }
    #selection-menu .highlight-btn.blue { background: #007bff; }
    #selection-menu .highlight-btn.red { background: #dc3545; }
    #selection-menu .highlight-btn.purple { background: #6f42c1; }

    /* 批注弹窗样式 */
    #annotation-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: none;
      z-index: 2000;
      align-items: center;
      justify-content: center;
    }

    #annotation-modal .modal-content {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      width: 90%;
      max-width: 500px;
      max-height: 80%;
      overflow-y: auto;
    }

    #annotation-modal h3 {
      margin-top: 0;
      color: #333;
    }

    #annotation-modal .selected-text {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      border-left: 4px solid #007bff;
      font-style: italic;
    }

    #annotation-modal textarea {
      width: 100%;
      height: 120px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      font-family: inherit;
      resize: vertical;
    }

    #annotation-modal .modal-buttons {
      margin-top: 15px;
      text-align: right;
    }

    #annotation-modal .modal-buttons button {
      margin-left: 10px;
      padding: 8px 16px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    }

    #annotation-modal .btn-primary {
      background: #007bff;
      color: #fff;
      border-color: #007bff;
    }

    #annotation-modal .btn-secondary {
      background: #6c757d;
      color: #fff;
      border-color: #6c757d;
    }

    /* 高亮和批注管理面板 */
    #annotations-panel {
      width: 300px;
      background: #f8f9fa;
      border-left: 1px solid #dee2e6;
      display: none;
      overflow-y: auto;
      padding: 15px;
    }

    #annotations-panel h3 {
      margin-top: 0;
      color: #333;
      border-bottom: 2px solid #007bff;
      padding-bottom: 10px;
    }

    .annotation-item {
      background: #fff;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .annotation-item:hover {
      border-color: #007bff;
      box-shadow: 0 2px 4px rgba(0,123,255,0.1);
    }

    .annotation-item .text-preview {
      font-weight: bold;
      margin-bottom: 8px;
      color: #333;
      font-size: 14px;
    }

    .annotation-item .note-preview {
      color: #666;
      font-size: 12px;
      margin-bottom: 8px;
      font-style: italic;
    }

    .annotation-item .highlight-color {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .annotation-item .actions {
      text-align: right;
    }

    .annotation-item .actions button {
      background: none;
      border: none;
      color: #007bff;
      cursor: pointer;
      font-size: 12px;
      margin-left: 8px;
    }

    .annotation-item .actions button:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div id="toolbar">
    <button id="openBtn">打开 EPUB</button>
    <button id="tocToggleBtn">目录</button>
    <button id="annotationsToggleBtn">批注</button>
    <button id="prevBtn">上一页</button>
    <button id="nextBtn">下一页</button>
    <button id="fullscreenBtn" style="margin-left:10px;">全屏</button>
    <span id="progress" style="margin-left:20px;color:#888;font-size:14px;"></span>
    <label style="margin-left:20px;">字体
      <select id="fontSize">
        <option value="14px">小</option>
        <option value="18px" selected>中</option>
        <option value="22px">大</option>
        <option value="26px">特大</option>
      </select>
    </label>
    <label style="margin-left:10px;">行距
      <select id="lineHeight">
        <option value="1.2">紧凑</option>
        <option value="1.6" selected>正常</option>
        <option value="2">宽松</option>
      </select>
    </label>
    <label style="margin-left:10px;">主题
      <select id="theme">
        <option value="light" selected>明亮</option>
        <option value="dark">夜间</option>
        <option value="sepia">护眼</option>
      </select>
    </label>
    <label style="margin-left:20px;">最近阅读
      <select id="recentBooks" style="min-width:180px;"><option value="">请选择...</option></select>
      <button id="clearRecentBtn" style="margin-left:5px;">清空历史</button>
    </label>
  </div>
  <div id="main-content">
    <div id="toc"></div>
    <div id="viewer">
      <!-- 选择菜单 -->
      <div id="selection-menu">
        <button class="highlight-btn yellow" data-color="yellow" title="黄色高亮">黄</button>
        <button class="highlight-btn green" data-color="green" title="绿色高亮">绿</button>
        <button class="highlight-btn blue" data-color="blue" title="蓝色高亮">蓝</button>
        <button class="highlight-btn red" data-color="red" title="红色高亮">红</button>
        <button class="highlight-btn purple" data-color="purple" title="紫色高亮">紫</button>
        <button id="annotate-btn" title="添加批注">批注</button>
        <button id="remove-highlight-btn" title="移除高亮">移除</button>
      </div>
    </div>
    <div id="annotations-panel">
      <h3>高亮与批注</h3>
      <div id="annotations-list"></div>
    </div>
  </div>

  <!-- 批注弹窗 -->
  <div id="annotation-modal">
    <div class="modal-content">
      <h3>添加批注</h3>
      <div class="selected-text" id="modal-selected-text"></div>
      <textarea id="annotation-text" placeholder="请输入您的批注..."></textarea>
      <div class="modal-buttons">
        <button id="cancel-annotation" class="btn-secondary">取消</button>
        <button id="save-annotation" class="btn-primary">保存</button>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js"></script>
  <script>window.JSZip = window.JSZip || JSZip;</script>
  <script src="https://cdn.jsdelivr.net/npm/epubjs/dist/epub.min.js"></script>
  <script src="renderer.js"></script>
</body>
</html>