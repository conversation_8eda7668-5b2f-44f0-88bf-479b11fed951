let book = null;
let rendition = null;
let currentLocation = null;
let tocState = JSON.parse(localStorage.getItem('tocState') || '{}');
let totalPages = 0;
let currentBookKey = '';
let locationsCacheKey = '';

const openBtn = document.getElementById('openBtn');
const tocToggleBtn = document.getElementById('tocToggleBtn');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const viewer = document.getElementById('viewer');
const tocDiv = document.getElementById('toc');
const progressSpan = document.getElementById('progress');
const fontSizeSelect = document.getElementById('fontSize');
const lineHeightSelect = document.getElementById('lineHeight');
const themeSelect = document.getElementById('theme');
const recentBooksSelect = document.getElementById('recentBooks');
const clearRecentBtn = document.getElementById('clearRecentBtn');
const fullscreenBtn = document.getElementById('fullscreenBtn');

// 高亮和批注相关元素
const annotationsToggleBtn = document.getElementById('annotationsToggleBtn');
const annotationsPanel = document.getElementById('annotations-panel');
const annotationsList = document.getElementById('annotations-list');
const selectionMenu = document.getElementById('selection-menu');
const annotationModal = document.getElementById('annotation-modal');
const modalSelectedText = document.getElementById('modal-selected-text');
const annotationText = document.getElementById('annotation-text');
const saveAnnotationBtn = document.getElementById('save-annotation');
const cancelAnnotationBtn = document.getElementById('cancel-annotation');
const annotateBtn = document.getElementById('annotate-btn');
const removeHighlightBtn = document.getElementById('remove-highlight-btn');
const clearCurrentPageBtn = document.getElementById('clearCurrentPageBtn');

// 高亮和批注数据
let annotations = [];
let currentSelection = null;
let currentAnnotationId = null;

console.log('window.electronAPI:', window.electronAPI);

tocToggleBtn.onclick = () => {
  if (tocDiv.style.display === 'block') {
    tocDiv.style.display = 'none';
  } else {
    tocDiv.style.display = 'block';
  }
};

// 批注面板切换
annotationsToggleBtn.onclick = () => {
  if (annotationsPanel.style.display === 'block') {
    annotationsPanel.style.display = 'none';
  } else {
    annotationsPanel.style.display = 'block';
    loadAnnotations();
    renderAnnotationsList();
  }
};

// 拖拽打开文件
window.addEventListener('dragover', (e) => {
  e.preventDefault();
});
window.addEventListener('drop', async (e) => {
  e.preventDefault();
  if (!e.dataTransfer || !e.dataTransfer.files || e.dataTransfer.files.length === 0) return;
  const file = e.dataTransfer.files[0];
  if (!file.name.endsWith('.epub')) {
    alert('只支持epub文件');
    return;
  }
  const filePath = file.path;
  await openEpubFileWithRecent(filePath);
});

// 主题样式定义
const themes = {
  light: {
    body: { background: '#fff', color: '#222' }
  },
  dark: {
    body: { background: '#181818', color: '#e6e6e6' }
  },
  sepia: {
    body: { background: '#f4ecd8', color: '#5b4636' }
  }
};

// 应用阅读样式
function applyReaderStyle() {
  if (!rendition) return;
  const fontSize = fontSizeSelect.value;
  const lineHeight = lineHeightSelect.value;
  const theme = themeSelect.value;
  rendition.themes.register('custom', {
    body: {
      ...themes[theme].body
    }
  });
  rendition.themes.select('custom');
  rendition.themes.override('font-size', fontSize);
  rendition.themes.override('line-height', lineHeight);
  // 保存设置
  localStorage.setItem('epub-reader-style', JSON.stringify({ fontSize, lineHeight, theme }));
}

// 监听控件变化
fontSizeSelect.onchange = applyReaderStyle;
lineHeightSelect.onchange = applyReaderStyle;
themeSelect.onchange = applyReaderStyle;

// 打开书后自动应用样式
function restoreReaderStyle() {
  const saved = localStorage.getItem('epub-reader-style');
  if (saved) {
    try {
      const { fontSize, lineHeight, theme } = JSON.parse(saved);
      if (fontSize) fontSizeSelect.value = fontSize;
      if (lineHeight) lineHeightSelect.value = lineHeight;
      if (theme) themeSelect.value = theme;
    } catch {}
  }
}

// 最近阅读最大数量
const RECENT_MAX = 10;

// 记录最近阅读，带书名
function addRecentBook(filePath, title) {
  let recent = JSON.parse(localStorage.getItem('epub-reader-recent') || '[]');
  // 去重，最新的放前面
  recent = recent.filter(item => item.path !== filePath);
  recent.unshift({ path: filePath, title });
  if (recent.length > RECENT_MAX) recent = recent.slice(0, RECENT_MAX);
  localStorage.setItem('epub-reader-recent', JSON.stringify(recent));
  renderRecentBooks();
}

// 渲染最近阅读下拉
function renderRecentBooks() {
  let recent = JSON.parse(localStorage.getItem('epub-reader-recent') || '[]');
  recentBooksSelect.innerHTML = '<option value="">请选择...</option>';
  recent.forEach(item => {
    const opt = document.createElement('option');
    opt.value = item.path;
    opt.textContent = item.title ? `${item.title} (${item.path})` : item.path;
    recentBooksSelect.appendChild(opt);
  });
}

// 选择最近阅读
recentBooksSelect.onchange = async function() {
  const filePath = this.value;
  if (filePath) {
    await openEpubFileWithRecent(filePath);
  }
};

// 清空历史
clearRecentBtn.onclick = function() {
  localStorage.removeItem('epub-reader-recent');
  renderRecentBooks();
};

// 打开书时记录历史，先获取书名
async function openEpubFileWithRecent(filePath) {
  // 先读取文件内容，获取书名
  const arrayBuffer = await window.electronAPI.readFile(filePath);
  const u8arr = new Uint8Array(arrayBuffer);
  const epubBlob = new Blob([u8arr], { type: 'application/epub+zip' });
  let title = '';
  try {
    const tempBook = ePub(epubBlob);
    const metadata = await tempBook.loaded.metadata;
    title = metadata.title || '';
    tempBook.destroy && tempBook.destroy();
  } catch {}
  addRecentBook(filePath, title);
  return openEpubFile(filePath);
}

// 封装打开epub的主逻辑，供按钮和拖拽复用
async function openEpubFile(filePath) {
  currentBookKey = 'epub-reader-cfi-' + filePath;
  locationsCacheKey = 'epub-reader-locations-' + filePath;
  console.log('拖拽或选择的文件路径:', filePath);
  if (!filePath) {
    viewer.innerHTML = '<div style="color:red">未选择文件</div>';
    return;
  }
  const arrayBuffer = await window.electronAPI.readFile(filePath);
  console.log('arrayBuffer:', arrayBuffer);
  if (!arrayBuffer) {
    viewer.innerHTML = '<div style="color:red">文件读取失败</div>';
    return;
  }
  const u8arr = new Uint8Array(arrayBuffer);
  console.log('u8arr.length:', u8arr.length);
  const epubBlob = new Blob([u8arr], { type: 'application/epub+zip' });
  console.log('epubBlob:', epubBlob);
  if (book) book.destroy();
  if (rendition) rendition.destroy();
  try {
    book = ePub(epubBlob);
    console.log('ePub book:', book);
    rendition = book.renderTo('viewer', { width: '100%', height: '80vh' });
    // 生成或加载全书分页
    progressSpan.textContent = '正在分页，请稍候...';
    let locationsData = localStorage.getItem(locationsCacheKey);
    let locationsLoaded = false;
    rendition.display().then(async () => {
      if (locationsData) {
        try {
          book.locations.load(JSON.parse(locationsData));
          totalPages = book.locations.length();
          locationsLoaded = true;
          progressSpan.textContent = '';
        } catch (e) {
          // 缓存损坏，重新生成
          locationsData = null;
        }
      }
      if (!locationsLoaded) {
        await book.locations.generate(1500);
        totalPages = book.locations.length();
        localStorage.setItem(locationsCacheKey, JSON.stringify(book.locations.save()));
        progressSpan.textContent = '';
      }
      // 跳转到上次阅读位置，只跳转一次
      const lastCfi = localStorage.getItem(currentBookKey);
      let hasJumped = false;
      if (lastCfi) {
        hasJumped = true;
        rendition.display(lastCfi);
      }
      rendition.on('relocated', (location) => {
        currentLocation = location;
        updateProgress(location);
        // 保存当前位置
        if (location && location.start && location.start.cfi) {
          localStorage.setItem(currentBookKey, location.start.cfi);
        }
        // 如果未跳转过且有lastCfi，首次 relocated 时跳转
        if (!hasJumped && lastCfi) {
          hasJumped = true;
          rendition.display(lastCfi);
        }
      });
      // 首次显示进度
      updateProgress(rendition.location);
    });
    // 加载目录
    book.loaded.navigation.then(nav => {
      renderTOC(nav.toc);
      tocDiv.style.display = 'block'; // 只在打开新书时显示目录
    });
    // 应用阅读样式
    restoreReaderStyle();
    applyReaderStyle();

    // 设置文本选择监听
    rendition.on('rendered', () => {
      const contents = rendition.getContents();
      contents.forEach(content => {
        const doc = content.document;

        // 监听文本选择
        doc.addEventListener('mouseup', handleTextSelection);
        doc.addEventListener('touchend', handleTextSelection);

        // 监听键盘选择
        doc.addEventListener('keyup', (e) => {
          if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' ||
              e.key === 'ArrowUp' || e.key === 'ArrowDown' ||
              e.shiftKey) {
            setTimeout(handleTextSelection, 10);
          }
        });
      });

      // 恢复高亮
      restoreHighlights();

      // 设置批注悬停提示
      setupAnnotationTooltips();
    });
  } catch (err) {
    viewer.innerHTML = '<div style="color:red">ePub 初始化失败：' + err + '</div>';
    console.error('ePub 初始化失败:', err);
  }
}

// 修改按钮打开逻辑为复用openEpubFile
openBtn.onclick = async () => {
  const filePath = await window.electronAPI.openFile();
  if (filePath) {
    await openEpubFileWithRecent(filePath);
  }
};

function updateProgress(location) {
  if (!location || !book || !rendition || !book.locations) {
    progressSpan.textContent = '';
    return;
  }
  // 当前页码
  let currentPage = 1;
  if (location && location.start && location.start.cfi) {
    currentPage = book.locations.locationFromCfi(location.start.cfi) + 1;
  } else if (location && location.cfi) {
    currentPage = book.locations.locationFromCfi(location.cfi) + 1;
  }
  // 总页数
  let total = totalPages || book.locations.length();
  // 百分比
  let percent = total > 0 ? Math.round((currentPage / total) * 1000) / 10 : 0;
  progressSpan.textContent = `进度：第${currentPage}页 / 共${total}页 (${percent}%)`;
}

function renderTOC(toc, parentPath = '') {
  if (!toc || toc.length === 0) {
    tocDiv.style.display = 'none';
    return;
  }
  tocDiv.innerHTML = '<h4 style="margin:10px 0 10px 10px;">目录</h4>';
  const ul = document.createElement('ul');
  ul.style.listStyle = 'none';
  ul.style.padding = '0 0 0 10px';
  toc.forEach((item, idx) => {
    const li = document.createElement('li');
    const itemPath = parentPath + '/' + idx;
    const hasSub = item.subitems && item.subitems.length > 0;
    // 目录项内容span
    const labelSpan = document.createElement('span');
    labelSpan.textContent = item.label;
    labelSpan.title = item.label;
    labelSpan.style.display = 'inline-block';
    labelSpan.style.width = 'calc(100% - 20px)';
    labelSpan.style.verticalAlign = 'middle';
    labelSpan.style.cursor = 'pointer';
    labelSpan.onclick = (e) => {
      e.stopPropagation();
      if (rendition && item.href) {
        rendition.display(item.href);
      }
    };
    li.appendChild(labelSpan);
    if (hasSub) {
      li.classList.add('toc-expandable');
      if (tocState[itemPath] !== false) {
        li.classList.add('toc-expanded');
      } else {
        li.classList.add('toc-collapsed');
      }
      li.onclick = (e) => {
        if (e.target !== li) return;
        const expanded = li.classList.toggle('toc-expanded');
        li.classList.toggle('toc-collapsed', !expanded);
        tocState[itemPath] = expanded;
        localStorage.setItem('tocState', JSON.stringify(tocState));
      };
    }
    ul.appendChild(li);
    if (hasSub) {
      const subUl = renderTOCSub(item.subitems, itemPath);
      li.appendChild(subUl);
      if (tocState[itemPath] === false) {
        subUl.style.display = 'none';
      }
    }
  });
  tocDiv.appendChild(ul);
}

function renderTOCSub(subitems, parentPath) {
  const subUl = document.createElement('ul');
  subUl.style.listStyle = 'none';
  subUl.style.paddingLeft = '16px';
  subitems.forEach((sub, idx) => {
    const subLi = document.createElement('li');
    const subPath = parentPath + '/' + idx;
    const labelSpan = document.createElement('span');
    labelSpan.textContent = sub.label;
    labelSpan.title = sub.label;
    labelSpan.style.display = 'inline-block';
    labelSpan.style.width = 'calc(100% - 20px)';
    labelSpan.style.verticalAlign = 'middle';
    labelSpan.style.cursor = 'pointer';
    labelSpan.onclick = (e) => {
      e.stopPropagation();
      if (rendition && sub.href) {
        rendition.display(sub.href);
      }
    };
    subLi.appendChild(labelSpan);
    if (sub.subitems && sub.subitems.length > 0) {
      subLi.classList.add('toc-expandable');
      if (tocState[subPath] !== false) {
        subLi.classList.add('toc-expanded');
      } else {
        subLi.classList.add('toc-collapsed');
      }
      subLi.onclick = (e) => {
        if (e.target !== subLi) return;
        const expanded = subLi.classList.toggle('toc-expanded');
        subLi.classList.toggle('toc-collapsed', !expanded);
        tocState[subPath] = expanded;
        localStorage.setItem('tocState', JSON.stringify(tocState));
      };
    }
    subUl.appendChild(subLi);
    if (sub.subitems && sub.subitems.length > 0) {
      const subSubUl = renderTOCSub(sub.subitems, subPath);
      subLi.appendChild(subSubUl);
      if (tocState[subPath] === false) {
        subSubUl.style.display = 'none';
      }
    }
  });
  return subUl;
}

prevBtn.onclick = () => {
  if (rendition) rendition.prev();
};
nextBtn.onclick = () => {
  if (rendition) rendition.next();
};

function base64ToBlob(base64, mime) {
  const byteChars = atob(base64);
  const byteNumbers = new Array(byteChars.length);
  for (let i = 0; i < byteChars.length; i++) {
    byteNumbers[i] = byteChars.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mime });
}

// 键盘快捷键：左右方向键/PageUp/PageDown 翻页
window.addEventListener('keydown', (e) => {
  if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) return;
  if (e.key === 'ArrowLeft' || e.key === 'PageUp') {
    if (rendition) rendition.prev();
    e.preventDefault();
  } else if (e.key === 'ArrowRight' || e.key === 'PageDown') {
    if (rendition) rendition.next();
    e.preventDefault();
  }
});

// 全屏切换
fullscreenBtn.onclick = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
    fullscreenBtn.textContent = '退出全屏';
  } else {
    document.exitFullscreen();
    fullscreenBtn.textContent = '全屏';
  }
};
document.addEventListener('fullscreenchange', () => {
  if (!document.fullscreenElement) {
    fullscreenBtn.textContent = '全屏';
  } else {
    fullscreenBtn.textContent = '退出全屏';
  }
  // 进入或退出全屏时重新调整epub渲染
  if (rendition) {
    rendition.resize();
  }
});

// 窗口尺寸变化时自适应
window.addEventListener('resize', () => {
  if (rendition) rendition.resize();
});

// 启动时渲染历史
renderRecentBooks();

// ==================== 高亮和批注功能 ====================

// 高亮颜色映射
const highlightColors = {
  yellow: '#ffeb3b'
};

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 获取当前书籍的批注存储键
function getAnnotationsKey() {
  return currentBookKey ? currentBookKey.replace('epub-reader-cfi-', 'epub-reader-annotations-') : '';
}

// 加载批注数据
function loadAnnotations() {
  const key = getAnnotationsKey();
  if (key) {
    const saved = localStorage.getItem(key);
    annotations = saved ? JSON.parse(saved) : [];
  } else {
    annotations = [];
  }
}

// 保存批注数据
function saveAnnotations() {
  const key = getAnnotationsKey();
  if (key) {
    localStorage.setItem(key, JSON.stringify(annotations));
  }
}

// 添加高亮
function addHighlight(cfiRange, color, text, note = '') {
  const annotation = {
    id: generateId(),
    type: note ? 'annotation' : 'highlight',
    cfiRange: cfiRange,
    color: color,
    text: text,
    note: note,
    timestamp: new Date().toISOString()
  };

  annotations.push(annotation);
  saveAnnotations();

  // 在epub中应用样式
  if (rendition) {
    if (note) {
      // 批注：不使用epub.js的annotations，直接操作DOM
      applyAnnotationStyle(annotation);
    } else {
      // 普通高亮：使用背景色
      rendition.annotations.add('highlight', cfiRange, {}, null, 'hl', {
        fill: highlightColors[color],
        'fill-opacity': '0.3',
        'mix-blend-mode': 'multiply'
      });
    }
  }

  return annotation;
}

// 应用批注样式
function applyAnnotationStyle(annotation) {
  if (!rendition || !annotation.note) return;

  setTimeout(() => {
    const contents = rendition.getContents();
    contents.forEach(content => {
      try {
        const range = content.range(annotation.cfiRange);
        if (range) {
          const doc = content.document;

          // 注入CSS样式（如果还没有）
          if (!doc.getElementById('annotation-styles')) {
            const style = doc.createElement('style');
            style.id = 'annotation-styles';
            style.textContent = `
              .epub-annotation-highlight {
                border-bottom: 2px dashed #007bff !important;
                background: transparent !important;
                cursor: help !important;
                position: relative;
              }
              .epub-annotation-highlight:hover {
                background-color: rgba(0, 123, 255, 0.1) !important;
              }
            `;
            doc.head.appendChild(style);
          }

          // 创建包装元素
          const wrapper = doc.createElement('span');
          wrapper.className = 'epub-annotation-highlight';
          wrapper.setAttribute('data-note', annotation.note);
          wrapper.setAttribute('data-annotation-id', annotation.id);

          // 添加悬停事件
          wrapper.addEventListener('mouseenter', showAnnotationTooltip);
          wrapper.addEventListener('mouseleave', hideAnnotationTooltip);

          try {
            range.surroundContents(wrapper);
          } catch (e) {
            console.warn('无法包装批注内容:', e);
          }
        }
      } catch (error) {
        console.warn('应用批注样式失败:', error);
      }
    });
  }, 100);
}

// 移除高亮
function removeHighlight(annotationId) {
  const annotation = annotations.find(a => a.id === annotationId);
  if (annotation) {
    // 从epub中移除样式
    if (rendition) {
      if (annotation.type === 'annotation') {
        // 移除批注的DOM元素
        removeAnnotationFromDOM(annotationId);
      } else {
        // 移除普通高亮
        rendition.annotations.remove(annotation.cfiRange, 'highlight');
      }
    }

    // 从数据中移除
    annotations = annotations.filter(a => a.id !== annotationId);
    saveAnnotations();
    renderAnnotationsList();
  }
}

// 从DOM中移除批注元素
function removeAnnotationFromDOM(annotationId) {
  if (!rendition) return;

  const contents = rendition.getContents();
  contents.forEach(content => {
    const doc = content.document;
    const annotationElements = doc.querySelectorAll(`[data-annotation-id="${annotationId}"]`);

    annotationElements.forEach(element => {
      // 将包装的内容移回原位置
      const parent = element.parentNode;
      while (element.firstChild) {
        parent.insertBefore(element.firstChild, element);
      }
      parent.removeChild(element);
    });
  });
}

// 渲染批注列表
function renderAnnotationsList() {
  annotationsList.innerHTML = '';

  if (annotations.length === 0) {
    annotationsList.innerHTML = '<p style="color: #666; text-align: center; margin-top: 20px;">暂无高亮和批注</p>';
    return;
  }

  annotations.forEach(annotation => {
    const item = document.createElement('div');
    item.className = 'annotation-item';

    const textPreview = annotation.text.length > 50
      ? annotation.text.substring(0, 50) + '...'
      : annotation.text;

    const notePreview = annotation.note
      ? (annotation.note.length > 30 ? annotation.note.substring(0, 30) + '...' : annotation.note)
      : '';

    const typeIndicator = annotation.type === 'annotation'
      ? '<span style="color: #007bff; font-size: 12px;">📝 批注</span>'
      : '<span style="color: #ffc107; font-size: 12px;">🖍️ 高亮</span>';

    item.innerHTML = `
      <div class="text-preview">${textPreview}</div>
      ${notePreview ? `<div class="note-preview">"${notePreview}"</div>` : ''}
      <div style="margin-bottom: 8px;">
        ${typeIndicator}
        <span style="font-size: 12px; color: #888; margin-left: 10px;">${new Date(annotation.timestamp).toLocaleString()}</span>
      </div>
      <div class="actions">
        <button onclick="goToAnnotation('${annotation.id}')">定位</button>
        ${annotation.type === 'annotation' ? '<button onclick="editAnnotation(\'' + annotation.id + '\')">编辑</button>' : ''}
        <button onclick="removeHighlight('${annotation.id}')">删除</button>
      </div>
    `;

    annotationsList.appendChild(item);
  });
}

// 定位到批注
function goToAnnotation(annotationId) {
  const annotation = annotations.find(a => a.id === annotationId);
  if (annotation && rendition) {
    rendition.display(annotation.cfiRange);
  }
}

// 编辑批注
function editAnnotation(annotationId) {
  const annotation = annotations.find(a => a.id === annotationId);
  if (annotation) {
    currentAnnotationId = annotationId;
    modalSelectedText.textContent = annotation.text;
    annotationText.value = annotation.note || '';
    annotationModal.style.display = 'flex';
  }
}

// 文本选择处理
function handleTextSelection() {
  try {
    const contents = rendition.getContents();
    if (!contents || contents.length === 0) return;

    const selection = contents[0].window.getSelection();

    if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
      hideSelectionMenu();
      return;
    }

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString().trim();

    if (selectedText.length === 0) {
      hideSelectionMenu();
      return;
    }

    // 获取CFI范围
    try {
      const cfiRange = contents[0].cfiFromRange(range);
      currentSelection = {
        cfiRange: cfiRange,
        text: selectedText,
        range: range
      };

      // 显示选择菜单
      showSelectionMenu(range);
    } catch (error) {
      console.error('获取CFI范围失败:', error);
      hideSelectionMenu();
    }
  } catch (error) {
    console.error('文本选择处理失败:', error);
  }
}

// 显示选择菜单
function showSelectionMenu(range) {
  const rect = range.getBoundingClientRect();
  const viewerRect = viewer.getBoundingClientRect();

  // 计算菜单位置
  const menuX = rect.left - viewerRect.left + (rect.width / 2);
  const menuY = rect.top - viewerRect.top - 50; // 在选择文本上方显示

  selectionMenu.style.left = menuX + 'px';
  selectionMenu.style.top = Math.max(10, menuY) + 'px';
  selectionMenu.style.display = 'block';

  // 调整菜单位置，确保不超出边界
  setTimeout(() => {
    const menuRect = selectionMenu.getBoundingClientRect();
    const viewerRect = viewer.getBoundingClientRect();

    if (menuRect.right > viewerRect.right) {
      selectionMenu.style.left = (viewerRect.right - menuRect.width - 10) + 'px';
    }
    if (menuRect.left < viewerRect.left) {
      selectionMenu.style.left = '10px';
    }
  }, 0);
}

// 隐藏选择菜单
function hideSelectionMenu() {
  selectionMenu.style.display = 'none';
  currentSelection = null;
}

// 恢复已保存的高亮
function restoreHighlights() {
  if (!rendition) return;

  loadAnnotations();
  annotations.forEach(annotation => {
    if (annotation.type === 'annotation') {
      // 批注：应用自定义样式
      applyAnnotationStyle(annotation);
    } else if (annotation.type === 'highlight') {
      // 普通高亮：使用背景色
      rendition.annotations.add('highlight', annotation.cfiRange, {}, null, 'hl', {
        fill: highlightColors[annotation.color],
        'fill-opacity': '0.3',
        'mix-blend-mode': 'multiply'
      });
    }
  });
}

// 设置批注悬停提示（现在主要用于重新应用所有批注）
function setupAnnotationTooltips() {
  if (!rendition) return;

  // 重新应用所有批注样式
  annotations.forEach(annotation => {
    if (annotation.type === 'annotation' && annotation.note) {
      applyAnnotationStyle(annotation);
    }
  });
}

// 显示批注提示框
function showAnnotationTooltip(event) {
  const element = event.target;
  const note = element.getAttribute('data-note');

  if (!note) return;

  // 创建提示框
  const tooltip = document.createElement('div');
  tooltip.className = 'annotation-tooltip';
  tooltip.textContent = note;
  tooltip.id = 'current-tooltip';

  // 添加到文档
  document.body.appendChild(tooltip);

  // 计算位置
  const rect = element.getBoundingClientRect();
  const tooltipRect = tooltip.getBoundingClientRect();

  let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
  let top = rect.top - tooltipRect.height - 10;

  // 确保不超出屏幕边界
  if (left < 10) left = 10;
  if (left + tooltipRect.width > window.innerWidth - 10) {
    left = window.innerWidth - tooltipRect.width - 10;
  }
  if (top < 10) {
    top = rect.bottom + 10;
    tooltip.style.transform = 'rotate(180deg)';
  }

  tooltip.style.left = left + 'px';
  tooltip.style.top = top + 'px';
  tooltip.style.display = 'block';
}

// 隐藏批注提示框
function hideAnnotationTooltip() {
  const tooltip = document.getElementById('current-tooltip');
  if (tooltip) {
    tooltip.remove();
  }
}

// ==================== 事件监听器 ====================

// 高亮按钮事件
document.querySelectorAll('.highlight-btn').forEach(btn => {
  btn.addEventListener('click', (e) => {
    if (!currentSelection) return;

    const color = e.target.dataset.color;
    addHighlight(currentSelection.cfiRange, color, currentSelection.text);
    hideSelectionMenu();

    // 清除选择
    try {
      const contents = rendition.getContents();
      if (contents && contents.length > 0) {
        const selection = contents[0].window.getSelection();
        selection.removeAllRanges();
      }
    } catch (error) {
      console.error('清除选择失败:', error);
    }

    // 更新批注列表
    if (annotationsPanel.style.display === 'block') {
      renderAnnotationsList();
    }
  });
});

// 批注按钮事件
annotateBtn.addEventListener('click', () => {
  if (!currentSelection) return;

  currentAnnotationId = null;
  modalSelectedText.textContent = currentSelection.text;
  annotationText.value = '';
  annotationModal.style.display = 'flex';
  hideSelectionMenu();
});

// 移除高亮按钮事件
removeHighlightBtn.addEventListener('click', () => {
  if (!currentSelection) return;

  // 查找当前选择位置的高亮或批注
  const annotation = annotations.find(a => {
    // 更精确的匹配逻辑
    return a.text.trim() === currentSelection.text.trim() ||
           a.cfiRange === currentSelection.cfiRange;
  });

  if (annotation) {
    removeHighlight(annotation.id);
    // 更新批注列表
    if (annotationsPanel.style.display === 'block') {
      renderAnnotationsList();
    }
  } else {
    alert('未找到对应的高亮或批注');
  }

  hideSelectionMenu();

  // 清除选择
  try {
    const contents = rendition.getContents();
    if (contents && contents.length > 0) {
      const selection = contents[0].window.getSelection();
      selection.removeAllRanges();
    }
  } catch (error) {
    console.error('清除选择失败:', error);
  }
});

// 批注弹窗事件
saveAnnotationBtn.addEventListener('click', () => {
  const noteText = annotationText.value.trim();

  if (currentAnnotationId) {
    // 编辑现有批注
    const annotation = annotations.find(a => a.id === currentAnnotationId);
    if (annotation) {
      annotation.note = noteText;
      saveAnnotations();
      renderAnnotationsList();
    }
  } else if (currentSelection) {
    // 添加新批注（虚线下划线）
    addHighlight(currentSelection.cfiRange, 'yellow', currentSelection.text, noteText);

    // 清除选择
    try {
      const contents = rendition.getContents();
      if (contents && contents.length > 0) {
        const selection = contents[0].window.getSelection();
        selection.removeAllRanges();
      }
    } catch (error) {
      console.error('清除选择失败:', error);
    }

    // 更新批注列表
    if (annotationsPanel.style.display === 'block') {
      renderAnnotationsList();
    }
  }

  annotationModal.style.display = 'none';
  currentAnnotationId = null;
});

cancelAnnotationBtn.addEventListener('click', () => {
  annotationModal.style.display = 'none';
  currentAnnotationId = null;
});

// 点击弹窗外部关闭
annotationModal.addEventListener('click', (e) => {
  if (e.target === annotationModal) {
    annotationModal.style.display = 'none';
    currentAnnotationId = null;
  }
});

// 点击其他地方隐藏选择菜单
document.addEventListener('click', (e) => {
  if (!selectionMenu.contains(e.target)) {
    hideSelectionMenu();
  }
});

// 清除当页高亮和批注
clearCurrentPageBtn.addEventListener('click', () => {
  if (!rendition || !currentLocation) {
    alert('请先打开EPUB文件');
    return;
  }

  if (confirm('确定要清除当前页面的所有高亮和批注吗？此操作不可撤销。')) {
    clearCurrentPageAnnotations();
  }
});

// 清除当前页面的所有高亮和批注
function clearCurrentPageAnnotations() {
  if (!rendition || !currentLocation) return;

  const currentCfi = currentLocation.start.cfi;
  let removedCount = 0;

  // 找到当前页面的所有批注
  const currentPageAnnotations = annotations.filter(annotation => {
    // 简单的CFI比较，检查是否在当前章节
    return annotation.cfiRange.includes(currentCfi.split('!')[0]);
  });

  // 移除当前页面的所有批注
  currentPageAnnotations.forEach(annotation => {
    removeHighlight(annotation.id);
    removedCount++;
  });

  // 更新批注列表
  if (annotationsPanel.style.display === 'block') {
    renderAnnotationsList();
  }

  // 重新设置批注提示
  setTimeout(() => {
    setupAnnotationTooltips();
  }, 100);

  alert(`已清除当前页面的 ${removedCount} 个高亮和批注`);
}

// 获取当前页面的CFI范围（更精确的实现）
function getCurrentPageCfiRange() {
  if (!rendition || !currentLocation) return null;

  try {
    return {
      start: currentLocation.start.cfi,
      end: currentLocation.end.cfi
    };
  } catch (error) {
    console.error('获取当前页面CFI范围失败:', error);
    return null;
  }
}