let book = null;
let rendition = null;
let currentLocation = null;
let tocState = JSON.parse(localStorage.getItem('tocState') || '{}');
let totalPages = 0;
let currentBookKey = '';
let locationsCacheKey = '';

const openBtn = document.getElementById('openBtn');
const tocToggleBtn = document.getElementById('tocToggleBtn');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const viewer = document.getElementById('viewer');
const tocDiv = document.getElementById('toc');
const progressSpan = document.getElementById('progress');
const fontSizeSelect = document.getElementById('fontSize');
const lineHeightSelect = document.getElementById('lineHeight');
const themeSelect = document.getElementById('theme');
const recentBooksSelect = document.getElementById('recentBooks');
const clearRecentBtn = document.getElementById('clearRecentBtn');
const fullscreenBtn = document.getElementById('fullscreenBtn');

// 高亮和批注相关元素
const annotationsToggleBtn = document.getElementById('annotationsToggleBtn');
const annotationsPanel = document.getElementById('annotations-panel');
const annotationsList = document.getElementById('annotations-list');
const selectionMenu = document.getElementById('selection-menu');
const annotationModal = document.getElementById('annotation-modal');
const modalSelectedText = document.getElementById('modal-selected-text');
const annotationText = document.getElementById('annotation-text');
const saveAnnotationBtn = document.getElementById('save-annotation');
const cancelAnnotationBtn = document.getElementById('cancel-annotation');
const annotateBtn = document.getElementById('annotate-btn');
const removeHighlightBtn = document.getElementById('remove-highlight-btn');

// 高亮和批注数据
let annotations = [];
let currentSelection = null;
let currentAnnotationId = null;

console.log('window.electronAPI:', window.electronAPI);

tocToggleBtn.onclick = () => {
  if (tocDiv.style.display === 'block') {
    tocDiv.style.display = 'none';
  } else {
    tocDiv.style.display = 'block';
  }
};

// 批注面板切换
annotationsToggleBtn.onclick = () => {
  if (annotationsPanel.style.display === 'block') {
    annotationsPanel.style.display = 'none';
  } else {
    annotationsPanel.style.display = 'block';
    loadAnnotations();
    renderAnnotationsList();
  }
};

// 拖拽打开文件
window.addEventListener('dragover', (e) => {
  e.preventDefault();
});
window.addEventListener('drop', async (e) => {
  e.preventDefault();
  if (!e.dataTransfer || !e.dataTransfer.files || e.dataTransfer.files.length === 0) return;
  const file = e.dataTransfer.files[0];
  if (!file.name.endsWith('.epub')) {
    alert('只支持epub文件');
    return;
  }
  const filePath = file.path;
  await openEpubFileWithRecent(filePath);
});

// 主题样式定义
const themes = {
  light: {
    body: { background: '#fff', color: '#222' }
  },
  dark: {
    body: { background: '#181818', color: '#e6e6e6' }
  },
  sepia: {
    body: { background: '#f4ecd8', color: '#5b4636' }
  }
};

// 应用阅读样式
function applyReaderStyle() {
  if (!rendition) return;
  const fontSize = fontSizeSelect.value;
  const lineHeight = lineHeightSelect.value;
  const theme = themeSelect.value;
  rendition.themes.register('custom', {
    body: {
      ...themes[theme].body
    }
  });
  rendition.themes.select('custom');
  rendition.themes.override('font-size', fontSize);
  rendition.themes.override('line-height', lineHeight);
  // 保存设置
  localStorage.setItem('epub-reader-style', JSON.stringify({ fontSize, lineHeight, theme }));
}

// 监听控件变化
fontSizeSelect.onchange = applyReaderStyle;
lineHeightSelect.onchange = applyReaderStyle;
themeSelect.onchange = applyReaderStyle;

// 打开书后自动应用样式
function restoreReaderStyle() {
  const saved = localStorage.getItem('epub-reader-style');
  if (saved) {
    try {
      const { fontSize, lineHeight, theme } = JSON.parse(saved);
      if (fontSize) fontSizeSelect.value = fontSize;
      if (lineHeight) lineHeightSelect.value = lineHeight;
      if (theme) themeSelect.value = theme;
    } catch {}
  }
}

// 最近阅读最大数量
const RECENT_MAX = 10;

// 记录最近阅读，带书名
function addRecentBook(filePath, title) {
  let recent = JSON.parse(localStorage.getItem('epub-reader-recent') || '[]');
  // 去重，最新的放前面
  recent = recent.filter(item => item.path !== filePath);
  recent.unshift({ path: filePath, title });
  if (recent.length > RECENT_MAX) recent = recent.slice(0, RECENT_MAX);
  localStorage.setItem('epub-reader-recent', JSON.stringify(recent));
  renderRecentBooks();
}

// 渲染最近阅读下拉
function renderRecentBooks() {
  let recent = JSON.parse(localStorage.getItem('epub-reader-recent') || '[]');
  recentBooksSelect.innerHTML = '<option value="">请选择...</option>';
  recent.forEach(item => {
    const opt = document.createElement('option');
    opt.value = item.path;
    opt.textContent = item.title ? `${item.title} (${item.path})` : item.path;
    recentBooksSelect.appendChild(opt);
  });
}

// 选择最近阅读
recentBooksSelect.onchange = async function() {
  const filePath = this.value;
  if (filePath) {
    await openEpubFileWithRecent(filePath);
  }
};

// 清空历史
clearRecentBtn.onclick = function() {
  localStorage.removeItem('epub-reader-recent');
  renderRecentBooks();
};

// 打开书时记录历史，先获取书名
async function openEpubFileWithRecent(filePath) {
  // 先读取文件内容，获取书名
  const arrayBuffer = await window.electronAPI.readFile(filePath);
  const u8arr = new Uint8Array(arrayBuffer);
  const epubBlob = new Blob([u8arr], { type: 'application/epub+zip' });
  let title = '';
  try {
    const tempBook = ePub(epubBlob);
    const metadata = await tempBook.loaded.metadata;
    title = metadata.title || '';
    tempBook.destroy && tempBook.destroy();
  } catch {}
  addRecentBook(filePath, title);
  return openEpubFile(filePath);
}

// 封装打开epub的主逻辑，供按钮和拖拽复用
async function openEpubFile(filePath) {
  currentBookKey = 'epub-reader-cfi-' + filePath;
  locationsCacheKey = 'epub-reader-locations-' + filePath;
  console.log('拖拽或选择的文件路径:', filePath);
  if (!filePath) {
    viewer.innerHTML = '<div style="color:red">未选择文件</div>';
    return;
  }
  const arrayBuffer = await window.electronAPI.readFile(filePath);
  console.log('arrayBuffer:', arrayBuffer);
  if (!arrayBuffer) {
    viewer.innerHTML = '<div style="color:red">文件读取失败</div>';
    return;
  }
  const u8arr = new Uint8Array(arrayBuffer);
  console.log('u8arr.length:', u8arr.length);
  const epubBlob = new Blob([u8arr], { type: 'application/epub+zip' });
  console.log('epubBlob:', epubBlob);
  if (book) book.destroy();
  if (rendition) rendition.destroy();
  try {
    book = ePub(epubBlob);
    console.log('ePub book:', book);
    rendition = book.renderTo('viewer', { width: '100%', height: '80vh' });
    // 生成或加载全书分页
    progressSpan.textContent = '正在分页，请稍候...';
    let locationsData = localStorage.getItem(locationsCacheKey);
    let locationsLoaded = false;
    rendition.display().then(async () => {
      if (locationsData) {
        try {
          book.locations.load(JSON.parse(locationsData));
          totalPages = book.locations.length();
          locationsLoaded = true;
          progressSpan.textContent = '';
        } catch (e) {
          // 缓存损坏，重新生成
          locationsData = null;
        }
      }
      if (!locationsLoaded) {
        await book.locations.generate(1500);
        totalPages = book.locations.length();
        localStorage.setItem(locationsCacheKey, JSON.stringify(book.locations.save()));
        progressSpan.textContent = '';
      }
      // 跳转到上次阅读位置，只跳转一次
      const lastCfi = localStorage.getItem(currentBookKey);
      let hasJumped = false;
      if (lastCfi) {
        hasJumped = true;
        rendition.display(lastCfi);
      }
      rendition.on('relocated', (location) => {
        currentLocation = location;
        updateProgress(location);
        // 保存当前位置
        if (location && location.start && location.start.cfi) {
          localStorage.setItem(currentBookKey, location.start.cfi);
        }
        // 如果未跳转过且有lastCfi，首次 relocated 时跳转
        if (!hasJumped && lastCfi) {
          hasJumped = true;
          rendition.display(lastCfi);
        }
      });
      // 首次显示进度
      updateProgress(rendition.location);
    });
    // 加载目录
    book.loaded.navigation.then(nav => {
      renderTOC(nav.toc);
      tocDiv.style.display = 'block'; // 只在打开新书时显示目录
    });
    // 应用阅读样式
    restoreReaderStyle();
    applyReaderStyle();

    // 设置文本选择监听
    rendition.on('rendered', () => {
      const contents = rendition.getContents();
      contents.forEach(content => {
        const doc = content.document;

        // 监听文本选择
        doc.addEventListener('mouseup', handleTextSelection);
        doc.addEventListener('touchend', handleTextSelection);

        // 监听键盘选择
        doc.addEventListener('keyup', (e) => {
          if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' ||
              e.key === 'ArrowUp' || e.key === 'ArrowDown' ||
              e.shiftKey) {
            setTimeout(handleTextSelection, 10);
          }
        });
      });

      // 恢复高亮
      restoreHighlights();
    });
  } catch (err) {
    viewer.innerHTML = '<div style="color:red">ePub 初始化失败：' + err + '</div>';
    console.error('ePub 初始化失败:', err);
  }
}

// 修改按钮打开逻辑为复用openEpubFile
openBtn.onclick = async () => {
  const filePath = await window.electronAPI.openFile();
  if (filePath) {
    await openEpubFileWithRecent(filePath);
  }
};

function updateProgress(location) {
  if (!location || !book || !rendition || !book.locations) {
    progressSpan.textContent = '';
    return;
  }
  // 当前页码
  let currentPage = 1;
  if (location && location.start && location.start.cfi) {
    currentPage = book.locations.locationFromCfi(location.start.cfi) + 1;
  } else if (location && location.cfi) {
    currentPage = book.locations.locationFromCfi(location.cfi) + 1;
  }
  // 总页数
  let total = totalPages || book.locations.length();
  // 百分比
  let percent = total > 0 ? Math.round((currentPage / total) * 1000) / 10 : 0;
  progressSpan.textContent = `进度：第${currentPage}页 / 共${total}页 (${percent}%)`;
}

function renderTOC(toc, parentPath = '') {
  if (!toc || toc.length === 0) {
    tocDiv.style.display = 'none';
    return;
  }
  tocDiv.innerHTML = '<h4 style="margin:10px 0 10px 10px;">目录</h4>';
  const ul = document.createElement('ul');
  ul.style.listStyle = 'none';
  ul.style.padding = '0 0 0 10px';
  toc.forEach((item, idx) => {
    const li = document.createElement('li');
    const itemPath = parentPath + '/' + idx;
    const hasSub = item.subitems && item.subitems.length > 0;
    // 目录项内容span
    const labelSpan = document.createElement('span');
    labelSpan.textContent = item.label;
    labelSpan.title = item.label;
    labelSpan.style.display = 'inline-block';
    labelSpan.style.width = 'calc(100% - 20px)';
    labelSpan.style.verticalAlign = 'middle';
    labelSpan.style.cursor = 'pointer';
    labelSpan.onclick = (e) => {
      e.stopPropagation();
      if (rendition && item.href) {
        rendition.display(item.href);
      }
    };
    li.appendChild(labelSpan);
    if (hasSub) {
      li.classList.add('toc-expandable');
      if (tocState[itemPath] !== false) {
        li.classList.add('toc-expanded');
      } else {
        li.classList.add('toc-collapsed');
      }
      li.onclick = (e) => {
        if (e.target !== li) return;
        const expanded = li.classList.toggle('toc-expanded');
        li.classList.toggle('toc-collapsed', !expanded);
        tocState[itemPath] = expanded;
        localStorage.setItem('tocState', JSON.stringify(tocState));
      };
    }
    ul.appendChild(li);
    if (hasSub) {
      const subUl = renderTOCSub(item.subitems, itemPath);
      li.appendChild(subUl);
      if (tocState[itemPath] === false) {
        subUl.style.display = 'none';
      }
    }
  });
  tocDiv.appendChild(ul);
}

function renderTOCSub(subitems, parentPath) {
  const subUl = document.createElement('ul');
  subUl.style.listStyle = 'none';
  subUl.style.paddingLeft = '16px';
  subitems.forEach((sub, idx) => {
    const subLi = document.createElement('li');
    const subPath = parentPath + '/' + idx;
    const labelSpan = document.createElement('span');
    labelSpan.textContent = sub.label;
    labelSpan.title = sub.label;
    labelSpan.style.display = 'inline-block';
    labelSpan.style.width = 'calc(100% - 20px)';
    labelSpan.style.verticalAlign = 'middle';
    labelSpan.style.cursor = 'pointer';
    labelSpan.onclick = (e) => {
      e.stopPropagation();
      if (rendition && sub.href) {
        rendition.display(sub.href);
      }
    };
    subLi.appendChild(labelSpan);
    if (sub.subitems && sub.subitems.length > 0) {
      subLi.classList.add('toc-expandable');
      if (tocState[subPath] !== false) {
        subLi.classList.add('toc-expanded');
      } else {
        subLi.classList.add('toc-collapsed');
      }
      subLi.onclick = (e) => {
        if (e.target !== subLi) return;
        const expanded = subLi.classList.toggle('toc-expanded');
        subLi.classList.toggle('toc-collapsed', !expanded);
        tocState[subPath] = expanded;
        localStorage.setItem('tocState', JSON.stringify(tocState));
      };
    }
    subUl.appendChild(subLi);
    if (sub.subitems && sub.subitems.length > 0) {
      const subSubUl = renderTOCSub(sub.subitems, subPath);
      subLi.appendChild(subSubUl);
      if (tocState[subPath] === false) {
        subSubUl.style.display = 'none';
      }
    }
  });
  return subUl;
}

prevBtn.onclick = () => {
  if (rendition) rendition.prev();
};
nextBtn.onclick = () => {
  if (rendition) rendition.next();
};

function base64ToBlob(base64, mime) {
  const byteChars = atob(base64);
  const byteNumbers = new Array(byteChars.length);
  for (let i = 0; i < byteChars.length; i++) {
    byteNumbers[i] = byteChars.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mime });
}

// 键盘快捷键：左右方向键/PageUp/PageDown 翻页
window.addEventListener('keydown', (e) => {
  if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) return;
  if (e.key === 'ArrowLeft' || e.key === 'PageUp') {
    if (rendition) rendition.prev();
    e.preventDefault();
  } else if (e.key === 'ArrowRight' || e.key === 'PageDown') {
    if (rendition) rendition.next();
    e.preventDefault();
  }
});

// 全屏切换
fullscreenBtn.onclick = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
    fullscreenBtn.textContent = '退出全屏';
  } else {
    document.exitFullscreen();
    fullscreenBtn.textContent = '全屏';
  }
};
document.addEventListener('fullscreenchange', () => {
  if (!document.fullscreenElement) {
    fullscreenBtn.textContent = '全屏';
  } else {
    fullscreenBtn.textContent = '退出全屏';
  }
  // 进入或退出全屏时重新调整epub渲染
  if (rendition) {
    rendition.resize();
  }
});

// 窗口尺寸变化时自适应
window.addEventListener('resize', () => {
  if (rendition) rendition.resize();
});

// 启动时渲染历史
renderRecentBooks();

// ==================== 高亮和批注功能 ====================

// 高亮颜色映射
const highlightColors = {
  yellow: '#ffeb3b',
  green: '#4caf50',
  blue: '#2196f3',
  red: '#f44336',
  purple: '#9c27b0'
};

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 获取当前书籍的批注存储键
function getAnnotationsKey() {
  return currentBookKey ? currentBookKey.replace('epub-reader-cfi-', 'epub-reader-annotations-') : '';
}

// 加载批注数据
function loadAnnotations() {
  const key = getAnnotationsKey();
  if (key) {
    const saved = localStorage.getItem(key);
    annotations = saved ? JSON.parse(saved) : [];
  } else {
    annotations = [];
  }
}

// 保存批注数据
function saveAnnotations() {
  const key = getAnnotationsKey();
  if (key) {
    localStorage.setItem(key, JSON.stringify(annotations));
  }
}

// 添加高亮
function addHighlight(cfiRange, color, text, note = '') {
  const annotation = {
    id: generateId(),
    type: 'highlight',
    cfiRange: cfiRange,
    color: color,
    text: text,
    note: note,
    timestamp: new Date().toISOString()
  };

  annotations.push(annotation);
  saveAnnotations();

  // 在epub中应用高亮
  if (rendition) {
    rendition.annotations.add('highlight', cfiRange, {}, null, 'hl', {
      fill: highlightColors[color],
      'fill-opacity': '0.3',
      'mix-blend-mode': 'multiply'
    });
  }

  return annotation;
}

// 移除高亮
function removeHighlight(annotationId) {
  const annotation = annotations.find(a => a.id === annotationId);
  if (annotation) {
    // 从epub中移除高亮
    if (rendition) {
      rendition.annotations.remove(annotation.cfiRange, 'highlight');
    }

    // 从数据中移除
    annotations = annotations.filter(a => a.id !== annotationId);
    saveAnnotations();
    renderAnnotationsList();
  }
}

// 渲染批注列表
function renderAnnotationsList() {
  annotationsList.innerHTML = '';

  if (annotations.length === 0) {
    annotationsList.innerHTML = '<p style="color: #666; text-align: center; margin-top: 20px;">暂无高亮和批注</p>';
    return;
  }

  annotations.forEach(annotation => {
    const item = document.createElement('div');
    item.className = 'annotation-item';

    const textPreview = annotation.text.length > 50
      ? annotation.text.substring(0, 50) + '...'
      : annotation.text;

    const notePreview = annotation.note
      ? (annotation.note.length > 30 ? annotation.note.substring(0, 30) + '...' : annotation.note)
      : '';

    item.innerHTML = `
      <div class="text-preview">${textPreview}</div>
      ${notePreview ? `<div class="note-preview">"${notePreview}"</div>` : ''}
      <div style="margin-bottom: 8px;">
        <span class="highlight-color" style="background: ${highlightColors[annotation.color]};"></span>
        <span style="font-size: 12px; color: #888;">${new Date(annotation.timestamp).toLocaleString()}</span>
      </div>
      <div class="actions">
        <button onclick="goToAnnotation('${annotation.id}')">定位</button>
        <button onclick="editAnnotation('${annotation.id}')">编辑</button>
        <button onclick="removeHighlight('${annotation.id}')">删除</button>
      </div>
    `;

    annotationsList.appendChild(item);
  });
}

// 定位到批注
function goToAnnotation(annotationId) {
  const annotation = annotations.find(a => a.id === annotationId);
  if (annotation && rendition) {
    rendition.display(annotation.cfiRange);
  }
}

// 编辑批注
function editAnnotation(annotationId) {
  const annotation = annotations.find(a => a.id === annotationId);
  if (annotation) {
    currentAnnotationId = annotationId;
    modalSelectedText.textContent = annotation.text;
    annotationText.value = annotation.note || '';
    annotationModal.style.display = 'flex';
  }
}

// 文本选择处理
function handleTextSelection() {
  try {
    const contents = rendition.getContents();
    if (!contents || contents.length === 0) return;

    const selection = contents[0].window.getSelection();

    if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
      hideSelectionMenu();
      return;
    }

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString().trim();

    if (selectedText.length === 0) {
      hideSelectionMenu();
      return;
    }

    // 获取CFI范围
    try {
      const cfiRange = contents[0].cfiFromRange(range);
      currentSelection = {
        cfiRange: cfiRange,
        text: selectedText,
        range: range
      };

      // 显示选择菜单
      showSelectionMenu(range);
    } catch (error) {
      console.error('获取CFI范围失败:', error);
      hideSelectionMenu();
    }
  } catch (error) {
    console.error('文本选择处理失败:', error);
  }
}

// 显示选择菜单
function showSelectionMenu(range) {
  const rect = range.getBoundingClientRect();
  const viewerRect = viewer.getBoundingClientRect();

  // 计算菜单位置
  const menuX = rect.left - viewerRect.left + (rect.width / 2);
  const menuY = rect.top - viewerRect.top - 50; // 在选择文本上方显示

  selectionMenu.style.left = menuX + 'px';
  selectionMenu.style.top = Math.max(10, menuY) + 'px';
  selectionMenu.style.display = 'block';

  // 调整菜单位置，确保不超出边界
  setTimeout(() => {
    const menuRect = selectionMenu.getBoundingClientRect();
    const viewerRect = viewer.getBoundingClientRect();

    if (menuRect.right > viewerRect.right) {
      selectionMenu.style.left = (viewerRect.right - menuRect.width - 10) + 'px';
    }
    if (menuRect.left < viewerRect.left) {
      selectionMenu.style.left = '10px';
    }
  }, 0);
}

// 隐藏选择菜单
function hideSelectionMenu() {
  selectionMenu.style.display = 'none';
  currentSelection = null;
}

// 恢复已保存的高亮
function restoreHighlights() {
  if (!rendition) return;

  loadAnnotations();
  annotations.forEach(annotation => {
    if (annotation.type === 'highlight') {
      rendition.annotations.add('highlight', annotation.cfiRange, {}, null, 'hl', {
        fill: highlightColors[annotation.color],
        'fill-opacity': '0.3',
        'mix-blend-mode': 'multiply'
      });
    }
  });
}

// ==================== 事件监听器 ====================

// 高亮按钮事件
document.querySelectorAll('.highlight-btn').forEach(btn => {
  btn.addEventListener('click', (e) => {
    if (!currentSelection) return;

    const color = e.target.dataset.color;
    addHighlight(currentSelection.cfiRange, color, currentSelection.text);
    hideSelectionMenu();

    // 清除选择
    try {
      const contents = rendition.getContents();
      if (contents && contents.length > 0) {
        const selection = contents[0].window.getSelection();
        selection.removeAllRanges();
      }
    } catch (error) {
      console.error('清除选择失败:', error);
    }

    // 更新批注列表
    if (annotationsPanel.style.display === 'block') {
      renderAnnotationsList();
    }
  });
});

// 批注按钮事件
annotateBtn.addEventListener('click', () => {
  if (!currentSelection) return;

  currentAnnotationId = null;
  modalSelectedText.textContent = currentSelection.text;
  annotationText.value = '';
  annotationModal.style.display = 'flex';
  hideSelectionMenu();
});

// 移除高亮按钮事件
removeHighlightBtn.addEventListener('click', () => {
  if (!currentSelection) return;

  // 查找当前选择位置的高亮
  const annotation = annotations.find(a =>
    a.cfiRange === currentSelection.cfiRange ||
    a.text === currentSelection.text
  );

  if (annotation) {
    removeHighlight(annotation.id);
  }

  hideSelectionMenu();

  // 清除选择
  try {
    const contents = rendition.getContents();
    if (contents && contents.length > 0) {
      const selection = contents[0].window.getSelection();
      selection.removeAllRanges();
    }
  } catch (error) {
    console.error('清除选择失败:', error);
  }
});

// 批注弹窗事件
saveAnnotationBtn.addEventListener('click', () => {
  const noteText = annotationText.value.trim();

  if (currentAnnotationId) {
    // 编辑现有批注
    const annotation = annotations.find(a => a.id === currentAnnotationId);
    if (annotation) {
      annotation.note = noteText;
      saveAnnotations();
      renderAnnotationsList();
    }
  } else if (currentSelection) {
    // 添加新批注（带高亮）
    addHighlight(currentSelection.cfiRange, 'yellow', currentSelection.text, noteText);

    // 清除选择
    try {
      const contents = rendition.getContents();
      if (contents && contents.length > 0) {
        const selection = contents[0].window.getSelection();
        selection.removeAllRanges();
      }
    } catch (error) {
      console.error('清除选择失败:', error);
    }

    // 更新批注列表
    if (annotationsPanel.style.display === 'block') {
      renderAnnotationsList();
    }
  }

  annotationModal.style.display = 'none';
  currentAnnotationId = null;
});

cancelAnnotationBtn.addEventListener('click', () => {
  annotationModal.style.display = 'none';
  currentAnnotationId = null;
});

// 点击弹窗外部关闭
annotationModal.addEventListener('click', (e) => {
  if (e.target === annotationModal) {
    annotationModal.style.display = 'none';
    currentAnnotationId = null;
  }
});

// 点击其他地方隐藏选择菜单
document.addEventListener('click', (e) => {
  if (!selectionMenu.contains(e.target)) {
    hideSelectionMenu();
  }
});